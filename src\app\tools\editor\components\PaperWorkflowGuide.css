/* 论文写作工作流程引导样式 */

.workflow-step {
  padding: 24px 0;
  min-height: 350px;
}

.workflow-step .ant-typography h4 {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.workflow-step .ant-typography-paragraph {
  margin-bottom: 24px;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

/* 模板选择卡片 */
.template-card {
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 8px;
  height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.template-card.selected {
  background: #f6ffed;
  border-color: #52c41a !important;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.template-card .ant-card-body {
  padding: 16px;
  width: 100%;
}

/* 步骤指示器优化 */
.ant-steps .ant-steps-item-process .ant-steps-item-icon {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-steps .ant-steps-item-finish .ant-steps-item-icon {
  background: #52c41a;
  border-color: #52c41a;
}

.ant-steps .ant-steps-item-wait .ant-steps-item-icon {
  background: #f5f5f5;
  border-color: #d9d9d9;
}

/* 表单优化 */
.workflow-step .ant-form-item {
  margin-bottom: 16px;
}

.workflow-step .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.workflow-step .ant-input,
.workflow-step .ant-select-selector {
  border-radius: 6px;
}

.workflow-step .ant-input:focus,
.workflow-step .ant-input-focused,
.workflow-step .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 信息卡片 */
.workflow-step .ant-card {
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.workflow-step .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.workflow-step .ant-card-body {
  padding: 16px;
}

/* 完成步骤样式 */
.workflow-step ul {
  margin: 8px 0 0 16px;
  padding: 0;
}

.workflow-step ul li {
  margin-bottom: 4px;
  color: #666;
  font-size: 14px;
}

/* 警告和成功提示 */
.workflow-step .ant-alert {
  border-radius: 6px;
  margin-top: 16px;
}

.workflow-step .ant-alert-success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.workflow-step .ant-alert-info {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

/* 按钮组优化 */
.ant-modal-footer .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-modal-footer .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-modal-footer .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-step {
    padding: 16px 0;
    min-height: 300px;
  }
  
  .template-card {
    height: 120px;
  }
  
  .workflow-step .ant-col {
    margin-bottom: 16px;
  }
  
  .ant-modal {
    margin: 0;
    max-width: 100vw;
    top: 0;
  }
  
  .ant-modal-content {
    border-radius: 0;
  }
}

/* 动画效果 */
.workflow-step {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.workflow-step .ant-spin-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图标优化 */
.workflow-step .anticon {
  font-size: 16px;
}

.template-card .anticon {
  font-size: 32px;
  margin-bottom: 12px;
}

/* 进度条优化 */
.workflow-step .ant-progress {
  margin-bottom: 16px;
}

.workflow-step .ant-progress-text {
  color: #666;
  font-size: 12px;
}

/* 分割线 */
.workflow-step .ant-divider {
  margin: 16px 0;
  border-color: #e8e8e8;
}

/* 工具提示 */
.workflow-step .ant-tooltip {
  max-width: 300px;
}

/* 选择器优化 */
.workflow-step .ant-select {
  width: 100%;
}

.workflow-step .ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 文本区域 */
.workflow-step .ant-input {
  resize: vertical;
  min-height: 32px;
}

.workflow-step textarea.ant-input {
  min-height: 80px;
}

/* 标签优化 */
.workflow-step .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 空状态 */
.workflow-step .ant-empty {
  margin: 40px 0;
}

.workflow-step .ant-empty-description {
  color: #999;
  font-size: 14px;
}

/* 高亮文本 */
.workflow-step .highlight {
  background: #fff2e8;
  padding: 2px 4px;
  border-radius: 3px;
  color: #fa8c16;
  font-weight: 500;
}

/* 成功状态 */
.workflow-step .success-text {
  color: #52c41a;
  font-weight: 500;
}

/* 错误状态 */
.workflow-step .error-text {
  color: #ff4d4f;
  font-weight: 500;
}

/* 信息文本 */
.workflow-step .info-text {
  color: #1890ff;
  font-weight: 500;
}

/* 次要文本 */
.workflow-step .secondary-text {
  color: #8c8c8c;
  font-size: 12px;
}

/* 卡片悬停效果 */
.workflow-step .ant-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 表单验证样式 */
.workflow-step .ant-form-item-has-error .ant-input,
.workflow-step .ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
}

.workflow-step .ant-form-item-has-error .ant-form-item-explain {
  color: #ff4d4f;
  font-size: 12px;
}

/* 成功验证样式 */
.workflow-step .ant-form-item-has-success .ant-input,
.workflow-step .ant-form-item-has-success .ant-select-selector {
  border-color: #52c41a;
}

/* 模态框标题 */
.ant-modal-header .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 步骤标题 */
.ant-steps-item-title {
  font-size: 14px;
  font-weight: 500;
}

.ant-steps-item-description {
  font-size: 12px;
  color: #8c8c8c;
}
